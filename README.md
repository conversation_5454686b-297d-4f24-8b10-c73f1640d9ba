# XHS Craw - UI Automator 项目

基于Spring Boot的Android UI自动化抓取工具，使用ADB UIAutomator获取页面XML并进行解析。

## 功能特性

1. **ADB命令封装**: 使用Apache Commons Exec执行ADB命令
2. **多设备支持**: 支持配置多个Android设备，可通过设备ID切换
3. **XML解析**: 使用JDOM2解析UIAutomator生成的XML文件
4. **Spring注入**: 全部使用Spring依赖注入，无new操作
5. **配置化管理**: 设备ID等参数通过YAML配置文件管理
6. **RESTful API**: 提供HTTP接口进行UI抓取和查询

## 项目结构

```
src/main/java/org/rdwl/xhscraw/
├── XhsCrawApplication.java          # Spring Boot启动类
├── constants/
│   └── AdbConstants.java           # ADB命令常量
├── config/
│   └── DeviceConfig.java           # 设备配置类
├── service/
│   ├── AdbExecutorService.java     # ADB命令执行服务
│   ├── XmlParsingService.java      # XML解析服务
│   └── UiAutomatorService.java     # UI Automator主服务
└── controller/
    └── UiAutomatorController.java  # REST控制器
```

## 配置说明

在 `application.yml` 中配置设备信息：

```yaml
adb:
  default-device-id: "emulator-5554"  # 默认设备ID
  local-storage-path: "./xml_dumps/"   # 本地XML存储路径
  command-timeout: 30000               # 命令超时时间(毫秒)
  debug-mode: true                     # 调试模式
  devices:                             # 设备列表
    - id: "emulator-5554"
      name: "Android模拟器"
      enabled: true
```

## API接口

### 1. 获取设备列表
```
GET /api/uiautomator/devices
```

### 2. 抓取UI元素（完整流程）
```
POST /api/uiautomator/capture?deviceId=emulator-5554
```

### 3. 查找特定UI元素
```
POST /api/uiautomator/find?deviceId=emulator-5554&resourceId=com.example:id/button&text=登录
```

### 4. 测试接口
```
POST /api/uiautomator/test?deviceId=emulator-5554
```

### 5. 获取配置信息
```
GET /api/uiautomator/config
```

### 6. 健康检查
```
GET /api/uiautomator/health
```

## 使用步骤

1. **启动应用**
   ```bash
   mvn spring-boot:run
   ```

2. **确保ADB可用**
   ```bash
   adb devices
   ```

3. **连接Android设备或启动模拟器**

4. **调用API进行UI抓取**
   ```bash
   curl -X POST "http://localhost:8080/api/uiautomator/test"
   ```

## 工作流程

1. **获取页面XML**: 使用 `adb shell uiautomator dump` 命令
2. **拉取到本地**: 使用 `adb pull` 命令将XML文件下载到本地
3. **解析XML**: 使用JDOM2解析XML文件，提取UI元素信息

## 依赖说明

- **Spring Boot**: Web框架和依赖注入
- **Apache Commons Exec**: 执行系统命令
- **JDOM2**: XML解析
- **Lombok**: 简化代码编写

## 注意事项

1. 确保ADB已安装并在PATH中
2. Android设备需要开启USB调试
3. 首次连接设备需要授权ADB调试
4. 模拟器需要先启动才能连接

## 错误处理

- 设备未连接: 检查ADB连接状态
- 权限不足: 确保已授权ADB调试
- 命令超时: 调整配置中的超时时间
- XML解析失败: 检查XML文件是否完整
