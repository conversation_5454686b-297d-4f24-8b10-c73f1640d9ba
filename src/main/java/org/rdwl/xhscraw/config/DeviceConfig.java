package org.rdwl.xhscraw.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 设备配置类
 * 从YAML配置文件中读取设备相关配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "adb")
public class DeviceConfig {
    
    /**
     * 默认设备ID
     */
    private String defaultDeviceId;
    
    /**
     * 可用设备列表
     */
    private List<Device> devices;
    
    /**
     * 本地存储路径配置
     */
    private String localStoragePath = "./xml_dumps/";
    
    /**
     * ADB命令超时时间（毫秒）
     */
    private long commandTimeout = 30000L;
    
    /**
     * 是否启用调试模式
     */
    private boolean debugMode = false;
    
    /**
     * 设备信息内部类
     */
    @Data
    public static class Device {
        /**
         * 设备ID
         */
        private String id;
        
        /**
         * 设备名称
         */
        private String name;
        
        /**
         * 设备描述
         */
        private String description;
        
        /**
         * 是否启用
         */
        private boolean enabled = true;
        
        /**
         * 设备特定配置
         */
        private Map<String, String> properties;
    }
    
    /**
     * 根据设备ID获取设备信息
     * @param deviceId 设备ID
     * @return 设备信息，如果未找到返回null
     */
    public Device getDeviceById(String deviceId) {
        if (devices == null) {
            return null;
        }
        return devices.stream()
                .filter(device -> device.getId().equals(deviceId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取启用的设备列表
     * @return 启用的设备列表
     */
    public List<Device> getEnabledDevices() {
        if (devices == null) {
            return List.of();
        }
        return devices.stream()
                .filter(Device::isEnabled)
                .toList();
    }
}
