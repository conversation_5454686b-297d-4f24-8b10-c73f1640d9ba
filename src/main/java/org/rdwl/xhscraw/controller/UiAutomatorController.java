package org.rdwl.xhscraw.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jdom2.JDOMException;
import org.rdwl.xhscraw.config.DeviceConfig;
import org.rdwl.xhscraw.service.UiAutomatorService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * UI Automator REST控制器
 * 提供UI抓取和解析的HTTP接口
 */
@Slf4j
@RestController
@RequestMapping("/uiautomator")
@RequiredArgsConstructor
public class UiAutomatorController {
    
    private final UiAutomatorService uiAutomatorService;
    private final DeviceConfig deviceConfig;
    
    /**
     * 获取设备列表
     */
    @GetMapping("/devices")
    public ResponseEntity<Map<String, Object>> getDevices() {
        try {
            String devicesInfo = uiAutomatorService.getConnectedDevices();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("timestamp", LocalDateTime.now());
            response.put("devicesInfo", devicesInfo);
            response.put("configuredDevices", deviceConfig.getDevices());
            response.put("defaultDeviceId", deviceConfig.getDefaultDeviceId());
            
            return ResponseEntity.ok(response);
        } catch (IOException e) {
            log.error("获取设备列表失败", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("获取设备列表失败: " + e.getMessage()));
        }
    }
    
    /**
     * 抓取UI元素（完整流程）
     */
    @PostMapping("/capture")
    public ResponseEntity<Map<String, Object>> captureUi(
            @RequestParam(required = false) String deviceId) {
        try {
            log.info("开始UI抓取，设备ID: {}", deviceId);
            
            UiAutomatorService.UiCaptureResult result = uiAutomatorService.captureUiElements(deviceId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("timestamp", LocalDateTime.now());
            response.put("deviceId", result.getDeviceId());
            response.put("startTime", result.getStartTime());
            response.put("endTime", result.getEndTime());
            response.put("duration", result.getDurationInfo());
            response.put("dumpSuccess", result.isDumpSuccess());
            response.put("pullSuccess", result.isPullSuccess());
            response.put("parseSuccess", result.isParseSuccess());
            response.put("localXmlPath", result.getLocalXmlPath());
            response.put("elementCount", result.getUiElements() != null ? result.getUiElements().size() : 0);
            response.put("elements", result.getUiElements());
            
            if (!result.isSuccess()) {
                response.put("errorMessage", result.getErrorMessage());
            }
            
            return ResponseEntity.ok(response);
            
        } catch (IOException | JDOMException e) {
            log.error("UI抓取失败", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("UI抓取失败: " + e.getMessage()));
        }
    }
    
    /**
     * 查找特定UI元素
     */
    @PostMapping("/find")
    public ResponseEntity<Map<String, Object>> findElements(
            @RequestParam(required = false) String deviceId,
            @RequestParam(required = false) String resourceId,
            @RequestParam(required = false) String text,
            @RequestParam(required = false) String className) {
        try {
            log.info("查找UI元素 - 设备: {}, resourceId: {}, text: {}, className: {}", 
                    deviceId, resourceId, text, className);
            
            UiAutomatorService.ElementSearchResult result = uiAutomatorService.findElements(
                    deviceId, resourceId, text, className);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("timestamp", LocalDateTime.now());
            response.put("deviceId", result.getDeviceId());
            response.put("searchTime", result.getSearchTime());
            response.put("totalElements", result.getTotalElements());
            response.put("matchCount", result.getMatchCount());
            response.put("matchedElements", result.getMatchedElements());
            response.put("searchCriteria", Map.of(
                    "resourceId", resourceId != null ? resourceId : "",
                    "text", text != null ? text : "",
                    "className", className != null ? className : ""
            ));
            
            return ResponseEntity.ok(response);
            
        } catch (IOException | JDOMException e) {
            log.error("查找UI元素失败", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("查找UI元素失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取配置信息
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getConfig() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("timestamp", LocalDateTime.now());
        response.put("defaultDeviceId", deviceConfig.getDefaultDeviceId());
        response.put("localStoragePath", deviceConfig.getLocalStoragePath());
        response.put("commandTimeout", deviceConfig.getCommandTimeout());
        response.put("debugMode", deviceConfig.isDebugMode());
        response.put("devices", deviceConfig.getDevices());
        response.put("enabledDevices", deviceConfig.getEnabledDevices());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("timestamp", LocalDateTime.now());
        response.put("service", "UI Automator Service");
        response.put("version", "1.0.0");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 测试接口 - 简单的UI抓取（不返回详细元素信息）
     */
    @PostMapping("/test")
    public ResponseEntity<Map<String, Object>> testCapture(
            @RequestParam(required = false) String deviceId) {
        try {
            log.info("测试UI抓取，设备ID: {}", deviceId);
            
            UiAutomatorService.UiCaptureResult result = uiAutomatorService.captureUiElements(deviceId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("timestamp", LocalDateTime.now());
            response.put("deviceId", result.getDeviceId());
            response.put("duration", result.getDurationInfo());
            response.put("elementCount", result.getUiElements() != null ? result.getUiElements().size() : 0);
            response.put("localXmlPath", result.getLocalXmlPath());
            
            // 只返回前5个元素作为示例
            if (result.getUiElements() != null && !result.getUiElements().isEmpty()) {
                int sampleSize = Math.min(5, result.getUiElements().size());
                response.put("sampleElements", result.getUiElements().subList(0, sampleSize));
            }
            
            if (!result.isSuccess()) {
                response.put("errorMessage", result.getErrorMessage());
            }
            
            return ResponseEntity.ok(response);
            
        } catch (IOException | JDOMException e) {
            log.error("测试UI抓取失败", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("测试UI抓取失败: " + e.getMessage()));
        }
    }
    
    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("timestamp", LocalDateTime.now());
        response.put("errorMessage", message);
        return response;
    }
}
