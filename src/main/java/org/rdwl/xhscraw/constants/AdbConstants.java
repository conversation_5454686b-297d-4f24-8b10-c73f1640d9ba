package org.rdwl.xhscraw.constants;

/**
 * ADB命令常量类
 * 定义所有ADB相关的命令常量，避免硬编码
 */
public final class AdbConstants {
    
    private AdbConstants() {
        // 私有构造函数，防止实例化
    }
    
    // ADB基础命令
    public static final String ADB_COMMAND = "adb";
    public static final String ADB_DEVICE_FLAG = "-s";
    
    // UI Automator相关命令
    public static final String UIAUTOMATOR_COMMAND = "uiautomator";
    public static final String UIAUTOMATOR_DUMP = "dump";
    public static final String UIAUTOMATOR_DUMP_FILE = "/sdcard/ui_dump.xml";
    
    // 文件操作命令
    public static final String PULL_COMMAND = "pull";
    public static final String PUSH_COMMAND = "push";
    public static final String SHELL_COMMAND = "shell";
    
    // 设备管理命令
    public static final String DEVICES_COMMAND = "devices";
    public static final String CONNECT_COMMAND = "connect";
    public static final String DISCONNECT_COMMAND = "disconnect";
    
    // 文件路径相关
    public static final String ANDROID_SDCARD_PATH = "/sdcard/";
    public static final String UI_DUMP_FILENAME = "ui_dump.xml";
    
    // 命令参数
    public static final String FORCE_FLAG = "--force";
    public static final String COMPRESSED_FLAG = "--compressed";
    
    // 超时设置（毫秒）
    public static final long DEFAULT_TIMEOUT = 30000L; // 30秒
    public static final long DUMP_TIMEOUT = 10000L;    // 10秒
    public static final long PULL_TIMEOUT = 15000L;    // 15秒
}
