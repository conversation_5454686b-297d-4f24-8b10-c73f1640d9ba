package org.rdwl.xhscraw.constants;

/**
 * ADB命令常量类
 * 定义所有ADB相关的命令常量，避免硬编码
 */
public final class AdbConstants {
    
    private AdbConstants() {
        // 私有构造函数，防止实例化
    }
    
    // 完整的ADB命令常量
    public static final String ADB_DEVICES = "adb devices";
    public static final String ADB_UIAUTOMATOR_DUMP = "adb shell uiautomator dump /sdcard/ui_dump.xml";
    public static final String ADB_PULL_XML = "adb pull /sdcard/ui_dump.xml";

    // 带设备ID的命令模板（使用%s占位符）
    public static final String ADB_DEVICES_WITH_ID = "adb -s %s devices";
    public static final String ADB_UIAUTOMATOR_DUMP_WITH_ID = "adb -s %s shell uiautomator dump /sdcard/ui_dump.xml";
    public static final String ADB_PULL_XML_WITH_ID = "adb -s %s pull /sdcard/ui_dump.xml %s";

    // 文件路径
    public static final String ANDROID_XML_PATH = "/sdcard/ui_dump.xml";
    public static final String LOCAL_XML_FILENAME = "ui_dump.xml";
    
    // 超时设置（毫秒）
    public static final long DEFAULT_TIMEOUT = 30000L; // 30秒
    public static final long DUMP_TIMEOUT = 10000L;    // 10秒
    public static final long PULL_TIMEOUT = 15000L;    // 15秒
}
