package org.rdwl.xhscraw.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jdom2.JDOMException;
import org.rdwl.xhscraw.config.DeviceConfig;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * UI Automator主服务
 * 协调整个流程：获取页面XML -> 拉取到本地 -> 解析XML
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UiAutomatorService {
    
    private final AdbExecutorService adbExecutorService;
    private final XmlParsingService xmlParsingService;
    private final DeviceConfig deviceConfig;
    
    /**
     * 执行完整的UI抓取流程
     * @param deviceId 设备ID，如果为null则使用默认设备
     * @return UI抓取结果
     * @throws IOException ADB命令执行异常
     * @throws JDOMException XML解析异常
     */
    public UiCaptureResult captureUiElements(String deviceId) throws IOException, JDOMException {
        String actualDeviceId = deviceId != null ? deviceId : deviceConfig.getDefaultDeviceId();
        log.info("开始执行UI抓取流程，设备ID: {}", actualDeviceId);
        
        UiCaptureResult result = new UiCaptureResult();
        result.setDeviceId(actualDeviceId);
        result.setStartTime(LocalDateTime.now());
        
        try {
            // 步骤1: 检查设备连接状态
            if (!adbExecutorService.isDeviceConnected(actualDeviceId)) {
                throw new IOException("设备未连接或不可用: " + actualDeviceId);
            }
            log.info("设备连接正常: {}", actualDeviceId);
            
            // 步骤2: 使用uiautomator dump获取页面XML
            log.info("步骤1: 获取页面XML");
            adbExecutorService.dumpUiXml(actualDeviceId);
            result.setDumpSuccess(true);
            
            // 步骤3: 拉取XML文件到本地
            log.info("步骤2: 拉取XML文件到本地");
            String localXmlPath = adbExecutorService.pullXmlFile(actualDeviceId, null);
            result.setLocalXmlPath(localXmlPath);
            result.setPullSuccess(true);
            
            // 步骤4: 解析XML文件
            log.info("步骤3: 解析XML文件");
            List<XmlParsingService.UiElement> elements = xmlParsingService.parseXmlFile(localXmlPath);
            result.setUiElements(elements);
            result.setParseSuccess(true);
            
            result.setEndTime(LocalDateTime.now());
            result.setSuccess(true);
            
            log.info("UI抓取流程完成，共解析出 {} 个UI元素，耗时: {} ms", 
                    elements.size(), 
                    java.time.Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
            
            return result;
            
        } catch (Exception e) {
            result.setEndTime(LocalDateTime.now());
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            log.error("UI抓取流程失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 获取设备列表
     * @return 设备信息
     * @throws IOException ADB命令执行异常
     */
    public String getConnectedDevices() throws IOException {
        return adbExecutorService.getDevices();
    }
    
    /**
     * 根据条件查找UI元素
     * @param deviceId 设备ID
     * @param resourceId 资源ID
     * @param text 文本内容
     * @param className 类名
     * @return 查找结果
     * @throws IOException ADB命令执行异常
     * @throws JDOMException XML解析异常
     */
    public ElementSearchResult findElements(String deviceId, String resourceId, String text, String className) 
            throws IOException, JDOMException {
        
        log.info("查找UI元素 - 设备: {}, resourceId: {}, text: {}, className: {}", 
                deviceId, resourceId, text, className);
        
        // 先抓取当前UI
        UiCaptureResult captureResult = captureUiElements(deviceId);
        
        if (!captureResult.isSuccess()) {
            throw new IOException("UI抓取失败: " + captureResult.getErrorMessage());
        }
        
        // 查找匹配的元素
        List<XmlParsingService.UiElement> matchedElements = xmlParsingService.findElements(
                captureResult.getUiElements(), resourceId, text, className);
        
        ElementSearchResult searchResult = new ElementSearchResult();
        searchResult.setDeviceId(deviceId);
        searchResult.setSearchTime(LocalDateTime.now());
        searchResult.setTotalElements(captureResult.getUiElements().size());
        searchResult.setMatchedElements(matchedElements);
        searchResult.setMatchCount(matchedElements.size());
        
        log.info("元素查找完成，匹配到 {} 个元素", matchedElements.size());
        return searchResult;
    }
    
    /**
     * UI抓取结果类
     */
    public static class UiCaptureResult {
        private String deviceId;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private boolean success;
        private boolean dumpSuccess;
        private boolean pullSuccess;
        private boolean parseSuccess;
        private String localXmlPath;
        private List<XmlParsingService.UiElement> uiElements;
        private String errorMessage;
        
        // Getters and Setters
        public String getDeviceId() { return deviceId; }
        public void setDeviceId(String deviceId) { this.deviceId = deviceId; }
        
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public boolean isDumpSuccess() { return dumpSuccess; }
        public void setDumpSuccess(boolean dumpSuccess) { this.dumpSuccess = dumpSuccess; }
        
        public boolean isPullSuccess() { return pullSuccess; }
        public void setPullSuccess(boolean pullSuccess) { this.pullSuccess = pullSuccess; }
        
        public boolean isParseSuccess() { return parseSuccess; }
        public void setParseSuccess(boolean parseSuccess) { this.parseSuccess = parseSuccess; }
        
        public String getLocalXmlPath() { return localXmlPath; }
        public void setLocalXmlPath(String localXmlPath) { this.localXmlPath = localXmlPath; }
        
        public List<XmlParsingService.UiElement> getUiElements() { return uiElements; }
        public void setUiElements(List<XmlParsingService.UiElement> uiElements) { this.uiElements = uiElements; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public String getDurationInfo() {
            if (startTime != null && endTime != null) {
                long duration = java.time.Duration.between(startTime, endTime).toMillis();
                return duration + "ms";
            }
            return "未知";
        }
    }
    
    /**
     * 元素搜索结果类
     */
    public static class ElementSearchResult {
        private String deviceId;
        private LocalDateTime searchTime;
        private int totalElements;
        private int matchCount;
        private List<XmlParsingService.UiElement> matchedElements;
        
        // Getters and Setters
        public String getDeviceId() { return deviceId; }
        public void setDeviceId(String deviceId) { this.deviceId = deviceId; }
        
        public LocalDateTime getSearchTime() { return searchTime; }
        public void setSearchTime(LocalDateTime searchTime) { this.searchTime = searchTime; }
        
        public int getTotalElements() { return totalElements; }
        public void setTotalElements(int totalElements) { this.totalElements = totalElements; }
        
        public int getMatchCount() { return matchCount; }
        public void setMatchCount(int matchCount) { this.matchCount = matchCount; }
        
        public List<XmlParsingService.UiElement> getMatchedElements() { return matchedElements; }
        public void setMatchedElements(List<XmlParsingService.UiElement> matchedElements) { this.matchedElements = matchedElements; }
    }
}
