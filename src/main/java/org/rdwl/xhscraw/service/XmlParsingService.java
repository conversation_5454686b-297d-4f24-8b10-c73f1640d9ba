package org.rdwl.xhscraw.service;

import lombok.extern.slf4j.Slf4j;
import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.JDOMException;
import org.jdom2.input.SAXBuilder;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * XML解析服务
 * 使用JDOM2解析UI Automator生成的XML文件
 */
@Slf4j
@Service
public class XmlParsingService {
    
    /**
     * 解析XML文件并返回UI元素信息
     * @param xmlFilePath XML文件路径
     * @return 解析后的UI元素列表
     * @throws IOException 文件读取异常
     * @throws JDOMException XML解析异常
     */
    public List<UiElement> parseXmlFile(String xmlFilePath) throws IOException, JDOMException {
        log.info("开始解析XML文件: {}", xmlFilePath);
        
        File xmlFile = new File(xmlFilePath);
        if (!xmlFile.exists()) {
            throw new IOException("XML文件不存在: " + xmlFilePath);
        }
        
        SAXBuilder builder = new SAXBuilder();
        Document document = builder.build(xmlFile);
        Element rootElement = document.getRootElement();
        
        List<UiElement> elements = new ArrayList<>();
        parseElement(rootElement, elements, 0);
        
        log.info("XML解析完成，共解析出 {} 个UI元素", elements.size());
        return elements;
    }
    
    /**
     * 递归解析XML元素
     * @param element 当前元素
     * @param elements 结果列表
     * @param depth 层级深度
     */
    private void parseElement(Element element, List<UiElement> elements, int depth) {
        UiElement uiElement = new UiElement();
        uiElement.setTagName(element.getName());
        uiElement.setDepth(depth);
        
        // 解析属性
        Map<String, String> attributes = new HashMap<>();
        element.getAttributes().forEach(attr -> 
            attributes.put(attr.getName(), attr.getValue())
        );
        uiElement.setAttributes(attributes);
        
        // 提取常用属性
        uiElement.setResourceId(attributes.get("resource-id"));
        uiElement.setText(attributes.get("text"));
        uiElement.setClassName(attributes.get("class"));
        uiElement.setPackageName(attributes.get("package"));
        uiElement.setContentDesc(attributes.get("content-desc"));
        uiElement.setClickable("true".equals(attributes.get("clickable")));
        uiElement.setEnabled("true".equals(attributes.get("enabled")));
        uiElement.setFocusable("true".equals(attributes.get("focusable")));
        uiElement.setScrollable("true".equals(attributes.get("scrollable")));
        
        // 解析bounds属性
        String bounds = attributes.get("bounds");
        if (bounds != null) {
            uiElement.setBounds(parseBounds(bounds));
        }
        
        elements.add(uiElement);
        
        // 递归解析子元素
        for (Element child : element.getChildren()) {
            parseElement(child, elements, depth + 1);
        }
    }
    
    /**
     * 解析bounds字符串，格式如: [0,0][1080,1920]
     * @param boundsStr bounds字符串
     * @return 边界信息
     */
    private Bounds parseBounds(String boundsStr) {
        try {
            // 移除方括号并分割
            String cleaned = boundsStr.replace("[", "").replace("]", ",");
            String[] parts = cleaned.split(",");
            
            if (parts.length >= 4) {
                return new Bounds(
                    Integer.parseInt(parts[0].trim()),
                    Integer.parseInt(parts[1].trim()),
                    Integer.parseInt(parts[2].trim()),
                    Integer.parseInt(parts[3].trim())
                );
            }
        } catch (NumberFormatException e) {
            log.warn("解析bounds失败: {}", boundsStr);
        }
        return null;
    }
    
    /**
     * 根据条件查找UI元素
     * @param elements UI元素列表
     * @param resourceId 资源ID
     * @param text 文本内容
     * @param className 类名
     * @return 匹配的元素列表
     */
    public List<UiElement> findElements(List<UiElement> elements, String resourceId, String text, String className) {
        return elements.stream()
                .filter(element -> {
                    boolean match = true;
                    if (resourceId != null && !resourceId.isEmpty()) {
                        match = resourceId.equals(element.getResourceId());
                    }
                    if (text != null && !text.isEmpty() && match) {
                        match = text.equals(element.getText());
                    }
                    if (className != null && !className.isEmpty() && match) {
                        match = className.equals(element.getClassName());
                    }
                    return match;
                })
                .toList();
    }
    
    /**
     * UI元素信息类
     */
    public static class UiElement {
        private String tagName;
        private int depth;
        private Map<String, String> attributes;
        private String resourceId;
        private String text;
        private String className;
        private String packageName;
        private String contentDesc;
        private boolean clickable;
        private boolean enabled;
        private boolean focusable;
        private boolean scrollable;
        private Bounds bounds;
        
        // Getters and Setters
        public String getTagName() { return tagName; }
        public void setTagName(String tagName) { this.tagName = tagName; }
        
        public int getDepth() { return depth; }
        public void setDepth(int depth) { this.depth = depth; }
        
        public Map<String, String> getAttributes() { return attributes; }
        public void setAttributes(Map<String, String> attributes) { this.attributes = attributes; }
        
        public String getResourceId() { return resourceId; }
        public void setResourceId(String resourceId) { this.resourceId = resourceId; }
        
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        
        public String getClassName() { return className; }
        public void setClassName(String className) { this.className = className; }
        
        public String getPackageName() { return packageName; }
        public void setPackageName(String packageName) { this.packageName = packageName; }
        
        public String getContentDesc() { return contentDesc; }
        public void setContentDesc(String contentDesc) { this.contentDesc = contentDesc; }
        
        public boolean isClickable() { return clickable; }
        public void setClickable(boolean clickable) { this.clickable = clickable; }
        
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        
        public boolean isFocusable() { return focusable; }
        public void setFocusable(boolean focusable) { this.focusable = focusable; }
        
        public boolean isScrollable() { return scrollable; }
        public void setScrollable(boolean scrollable) { this.scrollable = scrollable; }
        
        public Bounds getBounds() { return bounds; }
        public void setBounds(Bounds bounds) { this.bounds = bounds; }
    }
    
    /**
     * 边界信息类
     */
    public static class Bounds {
        private int left;
        private int top;
        private int right;
        private int bottom;
        
        public Bounds(int left, int top, int right, int bottom) {
            this.left = left;
            this.top = top;
            this.right = right;
            this.bottom = bottom;
        }
        
        public int getLeft() { return left; }
        public int getTop() { return top; }
        public int getRight() { return right; }
        public int getBottom() { return bottom; }
        
        public int getWidth() { return right - left; }
        public int getHeight() { return bottom - top; }
        
        @Override
        public String toString() {
            return String.format("[%d,%d][%d,%d]", left, top, right, bottom);
        }
    }
}
