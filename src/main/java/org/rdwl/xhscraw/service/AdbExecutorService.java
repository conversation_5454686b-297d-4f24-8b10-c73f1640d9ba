package org.rdwl.xhscraw.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.exec.*;
import org.rdwl.xhscraw.config.DeviceConfig;
import org.rdwl.xhscraw.constants.AdbConstants;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * ADB命令执行服务
 * 封装Apache Commons Exec执行ADB命令
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdbExecutorService {
    
    private final DeviceConfig deviceConfig;
    
    /**
     * 执行ADB命令并返回结果
     * @param command 要执行的命令
     * @return 命令执行结果
     * @throws IOException 执行异常
     */
    public String executeCommand(String command) throws IOException {
        log.debug("执行ADB命令: {}", command);
        
        CommandLine cmdLine = CommandLine.parse(command);
        DefaultExecutor executor = new DefaultExecutor();
        
        // 设置超时时间
        ExecuteWatchdog watchdog = new ExecuteWatchdog(deviceConfig.getCommandTimeout());
        executor.setWatchdog(watchdog);
        
        // 捕获输出
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ByteArrayOutputStream errorStream = new ByteArrayOutputStream();
        PumpStreamHandler streamHandler = new PumpStreamHandler(outputStream, errorStream);
        executor.setStreamHandler(streamHandler);
        
        try {
            int exitCode = executor.execute(cmdLine);
            String output = outputStream.toString();
            String error = errorStream.toString();
            
            if (deviceConfig.isDebugMode()) {
                log.debug("命令执行完成，退出码: {}", exitCode);
                log.debug("标准输出: {}", output);
                if (!error.isEmpty()) {
                    log.debug("错误输出: {}", error);
                }
            }
            
            if (exitCode != 0) {
                throw new IOException("命令执行失败，退出码: " + exitCode + ", 错误信息: " + error);
            }
            
            return output;
        } catch (ExecuteException e) {
            String error = errorStream.toString();
            log.error("ADB命令执行异常: {}, 错误输出: {}", e.getMessage(), error);
            throw new IOException("ADB命令执行失败: " + e.getMessage() + ", 错误信息: " + error, e);
        }
    }
    
    /**
     * 获取UI Automator XML文件
     * @param deviceId 设备ID，如果为null则使用默认设备
     * @return XML文件内容
     * @throws IOException 执行异常
     */
    public String dumpUiXml(String deviceId) throws IOException {
        String actualDeviceId = deviceId != null ? deviceId : deviceConfig.getDefaultDeviceId();
        String command = String.format(AdbConstants.ADB_UIAUTOMATOR_DUMP_WITH_ID, actualDeviceId);
        
        log.info("开始获取设备 {} 的UI XML", actualDeviceId);
        return executeCommand(command);
    }
    
    /**
     * 拉取XML文件到本地
     * @param deviceId 设备ID，如果为null则使用默认设备
     * @param localPath 本地存储路径，如果为null则使用配置的默认路径
     * @return 本地文件路径
     * @throws IOException 执行异常
     */
    public String pullXmlFile(String deviceId, String localPath) throws IOException {
        String actualDeviceId = deviceId != null ? deviceId : deviceConfig.getDefaultDeviceId();
        String actualLocalPath = localPath != null ? localPath : deviceConfig.getLocalStoragePath();
        
        // 确保本地目录存在
        Path localDir = Paths.get(actualLocalPath);
        if (!Files.exists(localDir)) {
            Files.createDirectories(localDir);
            log.info("创建本地存储目录: {}", localDir.toAbsolutePath());
        }
        
        // 构建完整的本地文件路径
        String localFilePath = Paths.get(actualLocalPath, AdbConstants.LOCAL_XML_FILENAME).toString();
        String command = String.format(AdbConstants.ADB_PULL_XML_WITH_ID, actualDeviceId, localFilePath);
        
        log.info("开始拉取设备 {} 的XML文件到本地: {}", actualDeviceId, localFilePath);
        executeCommand(command);
        
        // 验证文件是否成功拉取
        File localFile = new File(localFilePath);
        if (!localFile.exists()) {
            throw new IOException("XML文件拉取失败，本地文件不存在: " + localFilePath);
        }
        
        log.info("XML文件拉取成功: {}", localFilePath);
        return localFilePath;
    }
    
    /**
     * 获取连接的设备列表
     * @return 设备列表信息
     * @throws IOException 执行异常
     */
    public String getDevices() throws IOException {
        log.info("获取ADB设备列表");
        return executeCommand(AdbConstants.ADB_DEVICES);
    }
    
    /**
     * 检查指定设备是否连接
     * @param deviceId 设备ID
     * @return 是否连接
     */
    public boolean isDeviceConnected(String deviceId) {
        try {
            String devices = getDevices();
            return devices.contains(deviceId) && devices.contains("device");
        } catch (IOException e) {
            log.error("检查设备连接状态失败: {}", e.getMessage());
            return false;
        }
    }
}
