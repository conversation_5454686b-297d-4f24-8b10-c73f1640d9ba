spring:
  application:
    name: xhsCraw
  profiles:
    active: dev

# ADB设备配置
adb:
  # 默认设备ID（如果不指定设备，使用此设备）
  default-device-id: "emulator-5554"
  
  # 本地XML文件存储路径
  local-storage-path: "./xml_dumps/"
  
  # ADB命令超时时间（毫秒）
  command-timeout: 30000
  
  # 调试模式
  debug-mode: true
  
  # 可用设备列表
  devices:
    - id: "emulator-5554"
      name: "Android模拟器"
      description: "默认Android模拟器"
      enabled: true
      properties:
        type: "emulator"
        api-level: "30"
    
    - id: "*************:5555"
      name: "真实设备WiFi"
      description: "通过WiFi连接的真实设备"
      enabled: false
      properties:
        type: "real-device"
        connection: "wifi"

# 日志配置
logging:
  level:
    org.rdwl.xhscraw: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
      
adb:
  debug-mode: true
  command-timeout: 60000

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
      
adb:
  debug-mode: false
  command-timeout: 30000

logging:
  level:
    org.rdwl.xhscraw: INFO
